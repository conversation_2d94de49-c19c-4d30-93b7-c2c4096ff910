/**
 * Memory subsystem tests for PangeaFlow
 *
 * Covers:
 *  - InMemoryDriver CRUD operations
 *  - TTL expiry behaviour
 *  - Key listing & clearing
 *  - MemoryAgent default TTL propagation
 *  - Event emission & telemetry sanity
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import {
  memoryDrivers,
  MemoryAgent,
  EventBus,
  TelemetryCollector,
  ExecutionContext,
} from '../pangeaflow';

// ---------------------------------------------------------------------------
// Helper to build an ExecutionContext for MemoryAgent tests
// ---------------------------------------------------------------------------
function makeCtx(sharedState: Record<string, unknown>): ExecutionContext {
  return {
    id: Math.random().toString(36).slice(2),
    startTime: Date.now(),
    sharedState,
    trace: [],
    nodeOutputs: new Map(),
    events: [],
  } as ExecutionContext;
}

// ---------------------------------------------------------------------------
// InMemoryDriver behavioural contract
// ---------------------------------------------------------------------------
describe('InMemoryDriver', () => {
  let driver: ReturnType<typeof memoryDrivers.create>;

  beforeEach(() => {
    driver = memoryDrivers.create('mem');
  });

  it('stores and retrieves values', async () => {
    await driver.set('foo', 'bar');
    expect(await driver.get('foo')).toBe('bar');
  });

  it('respects TTLs', async () => {
    vi.useFakeTimers();
    await driver.set('temp', 123, 1000);
    expect(await driver.get('temp')).toBe(123);
    vi.advanceTimersByTime(1001);
    expect(await driver.get('temp')).toBeUndefined();
    vi.useRealTimers();
  });

  it('lists and clears keys', async () => {
    await driver.set('a', 1);
    await driver.set('b', 2);
    const keys = (await driver.keys()).sort();
    expect(keys).toEqual(['a', 'b']);
    await driver.clear();
    expect(await driver.keys()).toEqual([]);
  });
});

// ---------------------------------------------------------------------------
// MemoryAgent end‑to‑end operations
// ---------------------------------------------------------------------------
describe('MemoryAgent', () => {
  let bus: EventBus;
  let telemetry: TelemetryCollector;
  let driver: ReturnType<typeof memoryDrivers.create>;
  let agent: MemoryAgent;

  beforeEach(() => {
    bus = new EventBus();
    telemetry = new TelemetryCollector();
    driver = memoryDrivers.create('mem');
    agent = new MemoryAgent('memAgent' as any, bus, telemetry, driver, 2000);
  });

  it('stores via operation', async () => {
    const ctx = makeCtx({ operation: 'store', key: 'k1', value: 'v1' });
    await agent.execute(ctx);
    expect(await driver.get('k1')).toBe('v1');
  });

  it('retrieves via operation', async () => {
    await driver.set('k2', 'v2');
    const ctx = makeCtx({ operation: 'retrieve', key: 'k2' });
    const res = await agent.execute(ctx);
    expect(res.output).toBe('v2');
  });

  it('applies default TTL when none supplied', async () => {
    vi.useFakeTimers();
    const ctx = makeCtx({ operation: 'store', key: 'ttlKey', value: 42 });
    await agent.execute(ctx);
    vi.advanceTimersByTime(2001);
    expect(await driver.get('ttlKey')).toBeUndefined();
    vi.useRealTimers();
  });

  it('emits memory events', async () => {
    const events: string[] = [];
    bus.on('memory.stored', (e) => events.push(e.type));
    await agent.execute(makeCtx({ operation: 'store', key: 'k3', value: 'v3' }));
    expect(events).toContain('memory.stored');
  });
});
