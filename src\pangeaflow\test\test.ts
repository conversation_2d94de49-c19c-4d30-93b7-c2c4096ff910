/**
 * AgentFlow Usage Examples
 * 
 * Demonstrates building sophisticated AI agent workflows with the AgentFlow system
 */

import {
  WorkflowBuilder,
  WorkflowOrchestrator,
  StreamingWorkflow,
  AgentComponent,
  ExecutionContext,
  ExecutionResult,
} from '../pangeaflow';

// ============================================================================
// EXAMPLE 1: RESEARCH ASSISTANT WORKFLOW
// ============================================================================

async function createResearchAssistant(): Promise<WorkflowOrchestrator> {
  // Mock LLM provider (replace with real implementation)
  const llmProvider = async (prompt: string, context: Record<string, unknown>): Promise<string> => {
    // Simulate LLM processing
    await new Promise(resolve => setTimeout(resolve, 100));
    
    if (prompt.includes('research')) {
      return `Based on the research request, I need to:
      ACTIONS: search, analyze, summarize`;
    } else if (prompt.includes('search')) {
      return `Found relevant information about the topic.
      ACTIONS: store_findings, continue`;
    } else if (prompt.includes('analyze')) {
      return `Analysis complete. Key insights identified.
      ACTIONS: generate_report`;
    }
    
    return `Task completed successfully.
    ACTIONS: finish`;
  };

  // Available tools
  const tools = {
    web_search: async (args: { query: string }) => {
      console.log(`🔍 Searching for: ${args.query}`);
      return {
        results: [
          { title: 'Research Paper 1', url: 'https://example.com/1', snippet: 'Relevant findings...' },
          { title: 'Article 2', url: 'https://example.com/2', snippet: 'Key insights...' }
        ]
      };
    },
    
    analyze_data: async (args: { data: unknown }) => {
      console.log('📊 Analyzing data...');
      return {
        summary: 'Data shows significant trends in AI adoption',
        confidence: 0.85,
        insights: ['Trend 1', 'Trend 2', 'Trend 3']
      };
    },
    
    generate_report: async (args: { findings: unknown }) => {
      console.log('📝 Generating report...');
      return {
        report: 'Comprehensive research report generated',
        wordCount: 1500,
        sections: ['Executive Summary', 'Findings', 'Recommendations']
      };
    }
  };

  return WorkflowBuilder
    .create()
    .addReasoningAgent('planner', llmProvider)
    .addReasoningAgent('researcher', llmProvider) 
    .addReasoningAgent('analyst', llmProvider)
    .addToolAgent('tools', tools as Record<string, (args: unknown) => Promise<unknown>>)
    .addMemoryAgent('memory')
    .route('start', 'planner')
    .route('search', 'tools', 'memory')
    .route('analyze', 'analyst', 'tools')
    .route('summarize', 'analyst')
    .route('generate_report', 'tools')
    .route('store_findings', 'memory')
    .route('continue', 'planner')
    .route('finish', 'memory') // Final storage
    .build();
}

// ============================================================================
// EXAMPLE 2: CUSTOMER SERVICE AGENT
// ============================================================================

class CustomerServiceAgent extends AgentComponent {
  private readonly knowledgeBase = new Map([
    ['billing', 'For billing inquiries, please check your account dashboard or contact our billing team.'],
    ['technical', 'Technical issues can often be resolved by restarting the application or clearing cache.'],
    ['account', 'Account-related questions can be handled through your profile settings.']
  ]);

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    return this.withTelemetry('customer_service', async () => {
      const query = context.sharedState.query as string;
      const category = this.categorizeQuery(query);
      const response = this.knowledgeBase.get(category) || 'Let me connect you with a human agent.';
      
      this.emit('customer.query_processed', { query, category, response }, context.id);
      
      const needsEscalation = !this.knowledgeBase.has(category);
      
      return {
        success: true,
        output: { response, category, needsEscalation },
        events: [],
        nextActions: needsEscalation ? ['escalate'] : ['respond'],
        sharedStateUpdates: { category, confidence: needsEscalation ? 0.3 : 0.9 }
      };
    });
  }
  
  private categorizeQuery(query: string): string {
    const lowerQuery = query.toLowerCase();
    if (lowerQuery.includes('bill') || lowerQuery.includes('payment')) return 'billing';
    if (lowerQuery.includes('bug') || lowerQuery.includes('error')) return 'technical';
    if (lowerQuery.includes('account') || lowerQuery.includes('profile')) return 'account';
    return 'general';
  }
}

async function createCustomerServiceWorkflow(): Promise<WorkflowOrchestrator> {
  const orchestrator = new WorkflowOrchestrator();
  
  // Register custom customer service agent
  const csAgent = new CustomerServiceAgent(
    'customer_service' as any, 
    orchestrator['eventBus'], 
    orchestrator['telemetry']
  );
  
  orchestrator
    .registerComponent(csAgent)
    .defineRoute('start', ['customer_service' as any])
    .defineRoute('escalate', []) // End workflow for human handoff
    .defineRoute('respond', []); // End workflow with automated response
  
  return orchestrator;
}

// ============================================================================
// EXAMPLE 3: DATA PROCESSING PIPELINE
// ============================================================================

class DataProcessorAgent extends AgentComponent {
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    return this.withTelemetry('data_processing', async () => {
      const data = context.sharedState.data as any[];
      const operation = context.sharedState.operation as string;
      
      let processedData;
      
      switch (operation) {
        case 'clean':
          processedData = data.filter(item => item != null && item !== '');
          break;
        case 'transform':
          processedData = data.map(item => ({
            ...item,
            processed: true,
            timestamp: Date.now()
          }));
          break;
        case 'validate':
          processedData = data.filter(item => this.isValid(item));
          break;
        default:
          processedData = data;
      }
      
      this.emit('data.processed', { operation, inputCount: data.length, outputCount: processedData.length }, context.id);
      
      return {
        success: true,
        output: processedData,
        events: [],
        nextActions: ['continue'],
        sharedStateUpdates: { operation, inputCount: data.length, outputCount: processedData.length }
      };
    });
  }
  
  private isValid(item: any): boolean {
    return item && typeof item === 'object' && item.id != null;
  }
}

// ============================================================================
// DEMONSTRATION FUNCTIONS
// ============================================================================

async function demonstrateResearchWorkflow() {
  console.log('\n🔬 RESEARCH ASSISTANT DEMO');
  console.log('=' .repeat(50));
  
  const workflow = await createResearchAssistant();
  
  // Set up event monitoring
  workflow.on('reasoning.completed', (event) => {
    console.log(`💭 Reasoning: ${JSON.stringify(event.payload).substring(0, 100)}...`);
  });
  
  workflow.on('tool.executed', (event) => {
    console.log(`🔧 Tool executed: ${(event.payload as any).toolName}`);
  });
  
  workflow.on('memory.stored', (event) => {
    console.log(`💾 Stored in memory: ${(event.payload as any).key}`);
  });
  
  const results = await workflow.execute('start', {
    sharedState: { 
      query: 'Research the latest trends in AI and machine learning',
      depth: 'comprehensive'
    }
  });
  
  console.log(`\n✅ Research completed with ${results.length} steps`);
  console.log('📊 Workflow metrics:', workflow.getMetrics());
}

async function demonstrateCustomerService() {
  console.log('\n🎧 CUSTOMER SERVICE DEMO');
  console.log('=' .repeat(50));
  
  const workflow = await createCustomerServiceWorkflow();
  
  const testQueries = [
    'I have a billing question about my recent charge',
    'The app keeps crashing when I try to login', 
    'How do I update my account information?',
    'I need help with something not covered in FAQ'
  ];
  
  for (const query of testQueries) {
    console.log(`\n❓ Query: "${query}"`);
    
    const results = await workflow.execute('start', {
      sharedState: { query }
    });
    
    const response = results[0]?.output as any;
    console.log(`🤖 Response: ${response?.response}`);
    console.log(`📂 Category: ${response?.category}`);
    console.log(`🚨 Escalation needed: ${response?.needsEscalation ? 'Yes' : 'No'}`);
  }
}

async function demonstrateStreamingWorkflow() {
  console.log('\n🌊 STREAMING WORKFLOW DEMO');
  console.log('=' .repeat(50));
  
  const orchestrator = new WorkflowOrchestrator();
  
  // Register data processor
  const processor = new DataProcessorAgent(
    'processor' as any,
    orchestrator['eventBus'],
    orchestrator['telemetry']
  );
  
  orchestrator
    .registerComponent(processor)
    .defineRoute('process', ['processor' as any]);
  
  const streaming = new StreamingWorkflow(orchestrator);
  
  // Simulate streaming data
  async function* generateData() {
    for (let i = 0; i < 25; i++) {
      yield { id: i, value: Math.random(), name: `Item ${i}` };
      await new Promise(resolve => setTimeout(resolve, 50)); // Simulate real-time data
    }
  }
  
  let batchCount = 0;
  for await (const results of streaming.processStream(generateData(), 'process', 5)) {
    batchCount++;
    console.log(`📦 Batch ${batchCount} processed: ${results.length} items`);
    
    const totalProcessed = results.reduce((sum: number, result: ExecutionResult) => {
      return sum + ((result.output as any)?.outputCount || 0);
    }, 0);
    
    console.log(`✨ Total items processed in batch: ${totalProcessed}`);
  }
}

// ============================================================================
// MAIN DEMONSTRATION
// ============================================================================

async function runAllDemos() {
  try {
    await demonstrateResearchWorkflow();
    await demonstrateCustomerService();
    await demonstrateStreamingWorkflow();
    
    console.log('\n🎉 All demonstrations completed successfully!');
    
  } catch (error) {
    console.error('❌ Demo failed:', error);
  }
}

// Uncomment to run demos:
//runAllDemos();

export {
  createResearchAssistant,
  createCustomerServiceWorkflow,
  CustomerServiceAgent,
  DataProcessorAgent,
  runAllDemos
};
