/**
 * Tests for the new factory methods in PangeaFlow agents.
 * 
 * These tests verify that the factory methods work correctly and provide
 * the expected clean API while maintaining compatibility with the builder.
 */

import {
  ReasoningAgent,
  ToolAgent,
  MemoryAgent,
  WorkflowBuilder,
  EventBus,
  TelemetryCollector,
  type AgentOptions,
  type MemoryAgentOptions,
  type RetryOpts
} from '../pangeaflow';

// ============================================================================
// TEST HELPERS
// ============================================================================

async function mockLlmProvider(prompt: string, context: Record<string, unknown>): Promise<string> {
  return `Mock response for: ${prompt.substring(0, 50)}...\nACTIONS: continue`;
}

const mockTools = {
  'test_tool': async (args: any) => `Tool result: ${JSON.stringify(args)}`,
  'calculate': async (args: any) => args.a + args.b
};

// ============================================================================
// FACTORY METHOD TESTS
// ============================================================================

/**
 * Test ReasoningAgent factory method
 */
export function testReasoningAgentFactory() {
  console.log('🧪 Testing ReasoningAgent.create()...');

  // Test basic factory creation
  const agent1 = ReasoningAgent.create(
    'test-reasoning',
    mockLlmProvider
  );

  console.assert(agent1.id === 'test-reasoning', 'Agent ID should be set correctly');
  console.assert(typeof agent1.execute === 'function', 'Agent should have execute method');

  // Test with retry options
  const retryOpts: RetryOpts = {
    maxAttempts: 5,
    baseDelayMs: 1000,
    factor: 2,
    jitter: true
  };

  const agent2 = ReasoningAgent.create(
    'test-reasoning-with-retry',
    mockLlmProvider,
    retryOpts
  );

  console.assert(agent2.id === 'test-reasoning-with-retry', 'Agent ID should be set correctly');

  // Test with custom options
  const customEventBus = new EventBus();
  const customTelemetry = new TelemetryCollector();
  
  const agent3 = ReasoningAgent.create(
    'test-reasoning-custom',
    mockLlmProvider,
    {},
    {
      eventBus: customEventBus,
      telemetry: customTelemetry,
      initialState: { customKey: 'customValue' }
    }
  );

  console.assert(agent3.id === 'test-reasoning-custom', 'Agent ID should be set correctly');

  console.log('✅ ReasoningAgent factory tests passed');
  return { agent1, agent2, agent3 };
}

/**
 * Test ToolAgent factory method
 */
export function testToolAgentFactory() {
  console.log('🧪 Testing ToolAgent.create()...');

  // Test with Record
  const agent1 = ToolAgent.create(
    'test-tools',
    mockTools
  );

  console.assert(agent1.id === 'test-tools', 'Agent ID should be set correctly');
  console.assert(typeof agent1.execute === 'function', 'Agent should have execute method');

  // Test with Map
  const toolMap = new Map(Object.entries(mockTools));
  const agent2 = ToolAgent.create(
    'test-tools-map',
    toolMap
  );

  console.assert(agent2.id === 'test-tools-map', 'Agent ID should be set correctly');

  // Test with custom options
  const agent3 = ToolAgent.create(
    'test-tools-custom',
    mockTools,
    {
      initialState: { toolsLoaded: true }
    }
  );

  console.assert(agent3.id === 'test-tools-custom', 'Agent ID should be set correctly');

  console.log('✅ ToolAgent factory tests passed');
  return { agent1, agent2, agent3 };
}

/**
 * Test MemoryAgent factory method
 */
export function testMemoryAgentFactory() {
  console.log('🧪 Testing MemoryAgent.create()...');

  // Test basic factory creation
  const agent1 = MemoryAgent.create(
    'test-memory'
  );

  console.assert(agent1.id === 'test-memory', 'Agent ID should be set correctly');
  console.assert(typeof agent1.execute === 'function', 'Agent should have execute method');

  // Test with memory options
  const memoryOpts: MemoryAgentOptions = {
    driver: 'mem',
    defaultTTL: 3600000
  };

  const agent2 = MemoryAgent.create(
    'test-memory-with-opts',
    memoryOpts
  );

  console.assert(agent2.id === 'test-memory-with-opts', 'Agent ID should be set correctly');

  // Test with custom options
  const agent3 = MemoryAgent.create(
    'test-memory-custom',
    { driver: 'mem' },
    {
      initialState: { memoryInitialized: true }
    }
  );

  console.assert(agent3.id === 'test-memory-custom', 'Agent ID should be set correctly');

  console.log('✅ MemoryAgent factory tests passed');
  return { agent1, agent2, agent3 };
}

/**
 * Test integration with WorkflowBuilder
 * Note: The builder has its own methods and creates agents internally,
 * but we can test that factory-created agents work correctly
 */
export function testBuilderIntegration() {
  console.log('🧪 Testing WorkflowBuilder with standard methods...');

  // Build workflow using builder's own methods
  const workflow = WorkflowBuilder
    .create()
    .addReasoningAgent('reasoning' as any, mockLlmProvider)
    .addToolAgent('tools', mockTools)
    .addMemoryAgent('memory', { driver: 'mem', defaultTTL: 3600000 })
    .route('start', 'reasoning')
    .route('continue', 'tools', 'memory')
    .build();

  console.assert(workflow !== null, 'Workflow should be created successfully');

  console.log('✅ Builder integration tests passed');
  return workflow;
}

/**
 * Test workflow execution with factory-created agents
 */
export async function testWorkflowExecution() {
  console.log('🧪 Testing workflow execution with factory agents...');

  const workflow = testBuilderIntegration();

  try {
    const results = await workflow.execute('start', {
      sharedState: {
        userInput: 'Test input for factory agents',
        testData: { value: 42 }
      }
    });

    console.assert(Array.isArray(results), 'Results should be an array');
    console.assert(results.length > 0, 'Should have at least one result');

    // Check that at least one step was successful
    const hasSuccessfulStep = results.some(result => result.success);
    console.assert(hasSuccessfulStep, 'Should have at least one successful step');

    console.log('✅ Workflow execution tests passed');
    console.log(`   Executed ${results.length} steps`);
    
    return results;
  } catch (error) {
    console.error('❌ Workflow execution failed:', error);
    throw error;
  }
}

/**
 * Test parameter order and API cleanliness
 */
export function testApiCleanliness() {
  console.log('🧪 Testing API cleanliness and parameter order...');

  // The new factory methods should put business parameters first
  // and hide framework parameters completely

  // ReasoningAgent: id, callLlm, retryOpts (business params first)
  const reasoning = ReasoningAgent.create(
    'clean-reasoning',
    mockLlmProvider,
    { maxAttempts: 2 }
  );

  // ToolAgent: id, tools (business params first)
  const tools = ToolAgent.create(
    'clean-tools',
    mockTools
  );

  // MemoryAgent: id, memoryOpts (business params first)
  const memory = MemoryAgent.create(
    'clean-memory',
    { driver: 'mem' }
  );

  console.assert(reasoning.id === 'clean-reasoning', 'Reasoning agent created correctly');
  console.assert(tools.id === 'clean-tools', 'Tool agent created correctly');
  console.assert(memory.id === 'clean-memory', 'Memory agent created correctly');

  console.log('✅ API cleanliness tests passed');
  console.log('   ✓ Business parameters come first');
  console.log('   ✓ Framework parameters are hidden');
  console.log('   ✓ Clean, intuitive API');

  return { reasoning, tools, memory };
}

// ============================================================================
// TEST RUNNER
// ============================================================================

/**
 * Run all factory method tests
 */
export async function runAllTests() {
  console.log('🎯 Running PangeaFlow Factory Methods Tests\n');

  try {
    // Test individual factory methods
    testReasoningAgentFactory();
    console.log('');

    testToolAgentFactory();
    console.log('');

    testMemoryAgentFactory();
    console.log('');

    // Test integration
    testBuilderIntegration();
    console.log('');

    // Test API cleanliness
    testApiCleanliness();
    console.log('');

    // Test workflow execution
    await testWorkflowExecution();
    console.log('');

    console.log('🎉 All factory method tests passed!');
    console.log('✅ Factory methods provide clean API');
    console.log('✅ Business parameters come first');
    console.log('✅ Framework parameters are properly hidden');
    console.log('✅ Integration with WorkflowBuilder works correctly');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}
