{"name": "agentflow", "version": "1.0.0", "main": "index.js", "scripts": {"test": "vitest run", "test:watch": "vitest", "chat": "ts-node src/console-chat.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/node": "^22.15.24", "@vitest/coverage-v8": "^3.2.1", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vitest": "^3.2.1"}, "dependencies": {"@supabase/supabase-js": "^2.49.9", "dotenv": "^16.5.0"}}