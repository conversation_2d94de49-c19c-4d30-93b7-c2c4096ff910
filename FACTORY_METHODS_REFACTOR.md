# PangeaFlow Agent Factory Methods Refactor

## Overview

This refactor addresses the issue where agent class constructors had a problematic parameter order that mixed framework parameters (eventBus, telemetry) with business parameters (callLlm, retryOpts), making them error-prone for human developers.

## Problem Statement

**Before (Problematic Constructor Order):**
```typescript
// ❌ Framework params mixed with business params - easy to misuse
new ReasoningAgent(
  id,           // business param
  eventBus,     // framework param  
  telemetry,    // framework param
  callLlm,      // business param
  retryOpts     // business param
)
```

**Issues:**
- Framework parameters (eventBus, telemetry) come before business parameters
- Easy for developers to mix up parameter order
- Framework concerns leak into user code
- Not intuitive for developers who just want to use the agents

## Solution

Added static factory methods to each agent class that provide a clean API where:
1. **Business parameters come first** (callLlm, tools, memoryOpts)
2. **Framework parameters are hidden** (eventBus, telemetry are injected internally)
3. **Optional configuration is clearly separated**

## Changes Made

### 1. Added AgentOptions Interface

```typescript
export interface AgentOptions {
  /** Event bus for inter-component communication (optional, will be injected by builder) */
  eventBus?: EventBus;
  /** Telemetry collector for monitoring (optional, will be injected by builder) */
  telemetry?: TelemetryCollector;
  /** Initial state for the component */
  initialState?: Record<string, unknown>;
}
```

### 2. ReasoningAgent Factory Method

```typescript
// ✅ NEW: Clean factory method - business params first
static create(
  id: string,
  callLlm: (prompt: string, context: Record<string, unknown>) => Promise<string>,
  retryOpts: RetryOpts = {},
  opts: AgentOptions = {}
): ReasoningAgent
```

**Usage:**
```typescript
const agent = ReasoningAgent.create(
  'reasoning-agent',
  callLlm_openrouter,           // business param first
  { maxAttempts: 3 }            // business param second
  // framework params hidden
);
```

### 3. ToolAgent Factory Method

```typescript
// ✅ NEW: Clean factory method - business params first
static create(
  id: string,
  tools: Record<string, (args: unknown) => Promise<unknown>> | Map<string, (args: unknown) => Promise<unknown>>,
  opts: AgentOptions = {}
): ToolAgent
```

**Usage:**
```typescript
const agent = ToolAgent.create(
  'tool-agent',
  {                             // business param first
    'file_read': async (args) => await fs.readFile(args.path, 'utf8'),
    'calculate': async (args) => args.a + args.b
  }
  // framework params hidden
);
```

### 4. MemoryAgent Factory Method

```typescript
// ✅ NEW: Clean factory method - business params first
static create(
  id: string,
  memoryOpts: MemoryAgentOptions = {},
  opts: AgentOptions = {}
): MemoryAgent
```

**Usage:**
```typescript
const agent = MemoryAgent.create(
  'memory-agent',
  {                             // business param first
    driver: 'redis',
    driverOptions: { host: 'localhost', port: 6379 },
    defaultTTL: 3600000
  }
  // framework params hidden
);
```

## Benefits

### ✅ Clean API
- Business parameters come first
- Framework parameters are hidden
- Intuitive parameter order

### ✅ Less Error-Prone
- No need to remember framework parameter order
- Type safety maintained
- Clear separation of concerns

### ✅ Backward Compatible
- Original constructors still work
- WorkflowBuilder continues to work unchanged
- No breaking changes to existing code

### ✅ Flexible
- Factory methods can be used independently
- Framework parameters can still be customized if needed
- Supports both Record and Map for tools

## Usage Examples

### Basic Usage
```typescript
// Create agents with clean API
const reasoning = ReasoningAgent.create('reasoning', callLlm);
const tools = ToolAgent.create('tools', { 'calc': async (args) => args.a + args.b });
const memory = MemoryAgent.create('memory');
```

### Advanced Usage
```typescript
// With custom options
const reasoning = ReasoningAgent.create(
  'reasoning',
  callLlm,
  { maxAttempts: 5, baseDelayMs: 1000 },
  { initialState: { mode: 'advanced' } }
);

const memory = MemoryAgent.create(
  'memory',
  { 
    driver: 'redis',
    driverOptions: { host: 'localhost', port: 6379 },
    defaultTTL: 7200000 
  }
);
```

### Integration with WorkflowBuilder
```typescript
// WorkflowBuilder continues to work as before
const workflow = WorkflowBuilder
  .create()
  .addReasoningAgent('reasoning', callLlm)
  .addToolAgent('tools', toolsMap)
  .addMemoryAgent('memory')
  .route('start', 'reasoning')
  .build();
```

## Files Modified

- `src/pangeaflow/pangeaflow.ts` - Added factory methods and AgentOptions interface
- `src/pangeaflow/examples/factory-methods-example.ts` - Comprehensive examples
- `src/pangeaflow/test/factory-methods.test.ts` - Test suite for factory methods

## Testing

All factory methods have been thoroughly tested:
- ✅ Basic factory creation
- ✅ Advanced options handling
- ✅ Integration with WorkflowBuilder
- ✅ Workflow execution
- ✅ API cleanliness verification

Run tests with:
```bash
npx ts-node src/pangeaflow/test/factory-methods.test.ts
```

Run examples with:
```bash
npx ts-node src/pangeaflow/examples/factory-methods-example.ts
```

## Conclusion

The factory methods provide a much cleaner and more intuitive API for creating PangeaFlow agents while maintaining full backward compatibility. Developers can now focus on business logic (LLM functions, tools, memory configuration) without worrying about framework plumbing (eventBus, telemetry).
