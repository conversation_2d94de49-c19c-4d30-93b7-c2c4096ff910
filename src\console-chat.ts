#!/usr/bin/env node

/**
 * Console ChatGPT-like Application using PangeaFlow
 * 
 * A command-line interface for conversational AI powered by PangeaFlow
 * and OpenRouter LLM integration.
 */

import * as readline from 'readline';
import { ChatWorkflow } from './chat-workflow';

/**
 * Console colors for better UX
 */
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

/**
 * Main console chat application class
 */
class ConsoleChatApp {
  private rl: readline.Interface;
  private chatWorkflow: ChatWorkflow;
  private isRunning: boolean = false;

  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: `${colors.cyan}You: ${colors.reset}`
    });

    // Create a new chat workflow with a unique conversation ID
    const conversationId = `console-chat-${Date.now()}`;
    this.chatWorkflow = new ChatWorkflow(conversationId);
    
    // Setup event listeners for monitoring (optional)
    this.chatWorkflow.setupEventListeners();
  }

  /**
   * Displays the welcome message and instructions
   */
  private showWelcome(): void {
    console.clear();
    console.log(`${colors.bright}${colors.blue}╔══════════════════════════════════════════════════════════════╗${colors.reset}`);
    console.log(`${colors.bright}${colors.blue}║                    PangeaFlow Console Chat                   ║${colors.reset}`);
    console.log(`${colors.bright}${colors.blue}║                  Powered by OpenRouter AI                   ║${colors.reset}`);
    console.log(`${colors.bright}${colors.blue}╚══════════════════════════════════════════════════════════════╝${colors.reset}`);
    console.log();
    console.log(`${colors.dim}Commands:${colors.reset}`);
    console.log(`${colors.dim}  /help    - Show this help message${colors.reset}`);
    console.log(`${colors.dim}  /clear   - Clear conversation history${colors.reset}`);
    console.log(`${colors.dim}  /history - Show conversation history${colors.reset}`);
    console.log(`${colors.dim}  /metrics - Show workflow metrics${colors.reset}`);
    console.log(`${colors.dim}  /quit    - Exit the application${colors.reset}`);
    console.log();
    console.log(`${colors.green}Ready to chat! Type your message and press Enter.${colors.reset}`);
    console.log();
  }

  /**
   * Handles user commands
   */
  private async handleCommand(command: string): Promise<boolean> {
    const cmd = command.toLowerCase().trim();

    switch (cmd) {
      case '/help':
        this.showHelp();
        return true;

      case '/clear':
        this.chatWorkflow.clearConversation();
        console.log(`${colors.yellow}✨ Conversation history cleared.${colors.reset}`);
        return true;

      case '/history':
        this.showHistory();
        return true;

      case '/metrics':
        this.showMetrics();
        return true;

      case '/quit':
      case '/exit':
        console.log(`${colors.yellow}👋 Goodbye! Thanks for using PangeaFlow Console Chat.${colors.reset}`);
        return false;

      default:
        console.log(`${colors.red}❌ Unknown command: ${command}${colors.reset}`);
        console.log(`${colors.dim}Type /help for available commands.${colors.reset}`);
        return true;
    }
  }

  /**
   * Shows help information
   */
  private showHelp(): void {
    console.log(`${colors.bright}${colors.blue}Available Commands:${colors.reset}`);
    console.log(`${colors.cyan}  /help${colors.reset}    - Show this help message`);
    console.log(`${colors.cyan}  /clear${colors.reset}   - Clear conversation history`);
    console.log(`${colors.cyan}  /history${colors.reset} - Show conversation history`);
    console.log(`${colors.cyan}  /metrics${colors.reset} - Show workflow performance metrics`);
    console.log(`${colors.cyan}  /quit${colors.reset}    - Exit the application`);
    console.log();
  }

  /**
   * Shows conversation history
   */
  private showHistory(): void {
    const history = this.chatWorkflow.getConversationHistory();
    
    if (history.length === 0) {
      console.log(`${colors.dim}📝 No conversation history yet.${colors.reset}`);
      return;
    }

    console.log(`${colors.bright}${colors.blue}📝 Conversation History:${colors.reset}`);
    console.log();
    
    history.forEach((message, index) => {
      const timestamp = new Date(message.timestamp).toLocaleTimeString();
      const roleColor = message.role === 'user' ? colors.cyan : colors.green;
      const roleLabel = message.role === 'user' ? 'You' : 'AI';
      
      console.log(`${colors.dim}[${timestamp}]${colors.reset} ${roleColor}${roleLabel}:${colors.reset} ${message.content}`);
      if (index < history.length - 1) console.log();
    });
    console.log();
  }

  /**
   * Shows workflow metrics
   */
  private showMetrics(): void {
    const metrics = this.chatWorkflow.getMetrics();
    console.log(`${colors.bright}${colors.blue}📊 Workflow Metrics:${colors.reset}`);
    console.log(`${colors.dim}${JSON.stringify(metrics, null, 2)}${colors.reset}`);
    console.log();
  }

  /**
   * Processes user input
   */
  private async processInput(input: string): Promise<void> {
    const trimmedInput = input.trim();
    
    if (!trimmedInput) {
      return;
    }

    // Handle commands
    if (trimmedInput.startsWith('/')) {
      const shouldContinue = await this.handleCommand(trimmedInput);
      if (!shouldContinue) {
        this.stop();
        return;
      }
    } else {
      
      // Handle regular chat message
      await this.handleChatMessage(trimmedInput);
    }
  }

  /**
   * Handles a regular chat message
   */
  private async handleChatMessage(message: string): Promise<void> {
    try {
      // Show thinking indicator
      process.stdout.write(`${colors.dim}🤔 AI is thinking...${colors.reset}`);
      
      // Get response from chat workflow
      const response = await this.chatWorkflow.sendMessage(message);
      
      // Clear thinking indicator
      process.stdout.write('\r' + ' '.repeat(20) + '\r');
      
      // Display AI response
      console.log(`${colors.green}AI: ${colors.reset}${response}`);
      console.log();
      
    } catch (error) {
      // Clear thinking indicator
      process.stdout.write('\r' + ' '.repeat(20) + '\r');
      
      console.error(`${colors.red}❌ Error: ${error}${colors.reset}`);
      console.log();
    }
  }

  /**
   * Starts the chat application
   */
  public start(): void {
    this.isRunning = true;
    this.showWelcome();

    this.rl.on('line', async (input) => {
      if (this.isRunning) {
        await this.processInput(input);
        if (this.isRunning) {
          this.rl.prompt();
        }
      }
    });

    this.rl.on('close', () => {
      this.stop();
    });

    // Handle Ctrl+C gracefully
    process.on('SIGINT', () => {
      console.log(`\n${colors.yellow}👋 Goodbye! Thanks for using PangeaFlow Console Chat.${colors.reset}`);
      this.stop();
    });

    this.rl.prompt();
  }

  /**
   * Stops the chat application
   */
  public stop(): void {
    this.isRunning = false;
    this.rl.close();
    process.exit(0);
  }
}

// Start the application if this file is run directly
if (require.main === module) {
  const app = new ConsoleChatApp();
  app.start();
}

export { ConsoleChatApp };
