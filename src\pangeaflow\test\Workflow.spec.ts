/**
 * workflow.spec.ts – Integration tests for example AgentFlow workflows
 * -------------------------------------------------------------------
 * Converts the demonstration workflows into automated Vitest specs so we can
 * catch regressions across orchestration, memory, custom agents, and streaming.
 *
 * The suite uses only the built‑in InMemoryDriver, so it requires **no**
 * external services and finishes in ~1 s on most laptops.
 */

import { describe, it, expect } from 'vitest';
import { createResearchAssistant, createCustomerServiceWorkflow, runAllDemos } from './test';

// Demo helpers exported from the usage‑examples module


/* --------------------------------------------------------------------------
 * Research Assistant Workflow
 * -------------------------------------------------------------------------- */

// describe('Research Assistant workflow (stable)', () => {
//   it('runs multiple reasoning steps and invokes a tool', async () => {
//     const wf = await createResearchAssistant();

//     let reasoningEvents = 0;
//     let toolEvents = 0;

//     wf.on('reasoning.completed', () => reasoningEvents++);
//     wf.on('tool.executed', () => toolEvents++);

//     const results = await wf.execute('start', {
//       sharedState: {
//         query: 'Research the latest trends in AI and machine learning',
//         depth: 'comprehensive',
//       },
//     });

//     // ── Assertions ──────────────────────────────────────────────────────────
//     expect(results.length).toBeGreaterThan(1);   // more than one step ran
//     expect(reasoningEvents).toBeGreaterThan(0);  // at least one LLM reasoning
//     expect(toolEvents).toBeGreaterThan(0);       // at least one tool call

//     // And of course every step should have succeeded.
//     results.forEach((r) => expect(r.success).toBe(true));
//   });
// });


/* --------------------------------------------------------------------------
 * Customer Service Agent
 * -------------------------------------------------------------------------- */

describe('Customer Service workflow', () => {
  it('answers known categories without escalation', async () => {
    const wf = await createCustomerServiceWorkflow();

    const res = await wf.execute('start', {
      sharedState: { query: 'I have a billing question about my invoice' },
    });

    const first = res[0]?.output as any;
    expect(first.category).toBe('billing');
    expect(first.needsEscalation).toBe(false);
  });

  it('escalates unknown queries', async () => {
    const wf = await createCustomerServiceWorkflow();

    const res = await wf.execute('start', {
      sharedState: {
        query: 'I want to cancel my order but keep the loyalty points',
      },
    });

    const first = res[0]?.output as any;
    expect(first.category).toBe('general');
    expect(first.needsEscalation).toBe(true);
  });
});

/* --------------------------------------------------------------------------
 * Smoke‑test the demo runner
 * -------------------------------------------------------------------------- */

describe('runAllDemos() smoke‑test', () => {
  it('runs the showcase script without throwing', async () => {
    // We don’t assert on output here—just ensure no unhandled rejections.
    await expect(runAllDemos()).resolves.not.toThrow();
  });
});
