# PangeaFlow Console Chat

A console-based ChatGPT-like application built with PangeaFlow and OpenRouter AI integration.

## Features

- 🤖 **AI-Powered Conversations**: Uses OpenRouter's AI models for intelligent responses
- 💬 **Interactive Console Interface**: Clean, colorful command-line interface
- 📝 **Conversation History**: Maintains context throughout the chat session
- 🔧 **Built-in Commands**: Various utility commands for managing the chat
- 📊 **Workflow Metrics**: Monitor performance and usage statistics
- 🎯 **PangeaFlow Integration**: Leverages the full power of the PangeaFlow framework

## Quick Start

### Prerequisites

- Node.js (v14 or higher)
- TypeScript
- An OpenRouter API key (already configured in the codebase)

### Installation

1. Install dependencies:
```bash
npm install
```

2. Start the console chat:
```bash
npm run chat
```

## Usage

### Basic Chat

Simply type your message and press Enter to chat with the AI:

```
You: Hello, how are you today?
AI: Hello! I'm doing well, thank you for asking. I'm here and ready to help you with any questions or tasks you might have. How are you doing today?
```

### Available Commands

The application supports several built-in commands:

- `/help` - Show available commands
- `/clear` - Clear conversation history
- `/history` - Display conversation history
- `/metrics` - Show workflow performance metrics
- `/quit` or `/exit` - Exit the application

### Example Session

```
╔══════════════════════════════════════════════════════════════╗
║                    PangeaFlow Console Chat                   ║
║                  Powered by OpenRouter AI                   ║
╚══════════════════════════════════════════════════════════════╝

Commands:
  /help    - Show this help message
  /clear   - Clear conversation history
  /history - Show conversation history
  /metrics - Show workflow metrics
  /quit    - Exit the application

Ready to chat! Type your message and press Enter.

You: What is PangeaFlow?
🤔 AI is thinking...
AI: PangeaFlow is a next-generation agentic AI workflow system that provides a reactive, event-driven framework for building scalable AI agent workflows. It features built-in observability, error recovery, and dynamic orchestration capabilities.

You: /history
📝 Conversation History:

[10:30:15 AM] You: What is PangeaFlow?

[10:30:18 AM] AI: PangeaFlow is a next-generation agentic AI workflow system...

You: /quit
👋 Goodbye! Thanks for using PangeaFlow Console Chat.
```

## Architecture

The console chat application is built using several key components:

### ChatWorkflow (`src/chat-workflow.ts`)
- Manages the PangeaFlow workflow for conversational AI
- Handles conversation history and context
- Integrates with OpenRouter LLM via `callLlm_openrouter`
- Uses SessionMemory for state management

### ConsoleChatApp (`src/console-chat.ts`)
- Provides the interactive console interface
- Handles user input and command processing
- Manages the chat session lifecycle
- Implements colorful console output for better UX

### Key Features

1. **Event-Driven Architecture**: Uses PangeaFlow's event system for loose coupling
2. **Memory Management**: Maintains conversation context using SessionMemory
3. **Error Handling**: Graceful error recovery and user feedback
4. **Extensible Design**: Easy to add new commands and features

## Configuration

The application uses the following default settings:

- **Model**: `google/gemini-2.5-pro-exp-03-25`
- **Temperature**: `0.7` (for more creative responses)
- **History Limit**: Last 10 messages (to manage token limits)
- **Session Expiry**: 30 minutes

These can be modified in the `ChatWorkflow` constructor or by extending the class.

## Development

### Adding New Commands

To add a new command, modify the `handleCommand` method in `ConsoleChatApp`:

```typescript
case '/newcommand':
  // Your command logic here
  console.log('New command executed!');
  return true;
```

### Customizing the AI Model

You can change the AI model by modifying the `ChatWorkflow` constructor:

```typescript
const chatWorkflow = new ChatWorkflow(
  conversationId,
  "anthropic/claude-3-sonnet", // Different model
  0.5 // Different temperature
);
```

### Extending Functionality

The application is built on PangeaFlow, so you can easily extend it with:

- Tool agents for external API calls
- Memory agents for persistent storage
- Custom reasoning agents for specialized tasks
- Streaming workflows for large datasets

## Troubleshooting

### Common Issues

1. **"Session not found" error**: This usually resolves itself on the next message
2. **Network errors**: Check your internet connection and OpenRouter API status
3. **TypeScript compilation errors**: Run `npm install` to ensure all dependencies are installed

### Debug Mode

To enable debug logging, you can uncomment the logging lines in the `ChatWorkflow.setupEventListeners()` method.

## License

This project is part of the PangeaFlow framework and follows the same licensing terms.
