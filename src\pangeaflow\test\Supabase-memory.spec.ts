/**
 * SupabaseMemoryDriver integration tests
 * -------------------------------------------------------------
 * These tests hit a **real Supabase project**.  Provide the creds via
 * environment variables before running:
 *
 *   SUPABASE_URL        e.g. https://xyzcompany.supabase.co
 *   SUPABASE_ANON_KEY   the anon or service role key
 *
 * The suite generates an isolated `namespace` for every run, so it
 * won't clash with other data.  TTL expiry tests rely only on client-
 * side checks (the driver evaluates `expires` on read), so you DON'T
 * need Postgres cron GC configured for them to pass.
 * -------------------------------------------------------------
 */

import { describe, it, expect, beforeAll, afterAll, vi } from 'vitest';
import { randomUUID } from 'crypto';
import { memoryDrivers, MemoryDriver } from '../pangeaflow';
import { registerSupabaseMemoryDriver } from '../memory-drivers';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const SUPA_URL = process.env.SUPABASE_URL;
const SUPA_KEY = process.env.SUPABASE_ANON_KEY;

if (!SUPA_URL || !SUPA_KEY) {
  throw new Error(
    '⚠️  Supabase tests require SUPABASE_URL & SUPABASE_ANON_KEY env vars'
  );
}

let driver: MemoryDriver;
const namespace = `tests_${randomUUID()}`;

beforeAll(() => {
  // install driver once for the whole suite
  registerSupabaseMemoryDriver();

  driver = memoryDrivers.create('supabase', {
    url: SUPA_URL,
    anonKey: SUPA_KEY,
    namespace,
  });
});

afterAll(async () => {
  await driver.clear(namespace); // clean up rows we created
  await driver.close();
});

describe('SupabaseMemoryDriver lifecycle', () => {
  it('clears → sets → reads → deletes → verifies → sets-again', async () => {
    /* ----------------------------------------------------------- */
    /* 1. clear any leftovers (noop on first run)                  */
    await driver.clear(namespace);

    /* 2. write first record with unique key */
    const key1 = `first_${Date.now()}`;
    const val1 = { foo: 'bar', stamp: key1 };
    await driver.set(key1, val1);

    /* 3. read it back */
    const fetched1 = await driver.get<typeof val1>(key1);
    expect(fetched1).toEqual(val1);

    /* 4. delete the record */
    await driver.delete(key1);

    /* 5. verify deletion */
    const shouldBeGone = await driver.get(key1);
    expect(shouldBeGone).toBeUndefined();

    /* 7. write a second, new record */
    const key2 = `second_${Date.now()}`;
    const val2 = { baz: 123, stamp: key2 };
    await driver.set(key2, val2);

    const fetched2 = await driver.get<typeof val2>(key2);
    expect(fetched2).toEqual(val2);
  });
});

describe('SupabaseMemoryDriver – basic CRUD', () => {
  it('stores & retrieves a value', async () => {
    await driver.set('foo', { bar: 42 });
    const out = await driver.get<{ bar: number }>('foo');
    expect(out?.bar).toBe(42);
  });

  it('lists keys after writes', async () => {
    await driver.set('alpha', 1);
    await driver.set('beta', 2);
    const keys = await driver.keys(namespace);
    expect(keys.sort()).toEqual(expect.arrayContaining(['foo', 'alpha', 'beta'].sort()));
  });

  it('deletes a key', async () => {
    await driver.set('todelete', 'bye');
    await driver.delete('todelete');
    const gone = await driver.get('todelete');
    expect(gone).toBeUndefined();
  });
});

describe('SupabaseMemoryDriver – TTL handling', () => {
  vi.useFakeTimers();

  it('honours TTL expiration on read', async () => {
    await driver.set('ephemeral', 'short', 1000); // 1 s
    vi.advanceTimersByTime(500);
    expect(await driver.get('ephemeral')).toBe('short'); // still there

    vi.advanceTimersByTime(600); // total 1100 ms
    expect(await driver.get('ephemeral')).toBeUndefined(); // expired
  });

  afterAll(() => {
    vi.useRealTimers();
  });
});
