// create table if not exists pangeaflow_memory (
//   namespace text    not null,
//   key        text   not null,
//   value      jsonb,
//   expires    bigint,
//   primary key (namespace, key)
// );

// -- optional: prune expired rows automatically every hour
// create or replace function pangeaflow_memory_gc() returns void
// language plpgsql as $$
//   delete from pangeaflow_memory where expires is not null and expires < extract(epoch from now())*1000;
// $$;
// create extension if not exists pg_cron;
// select cron.schedule('0 * * * *', $$select pangeaflow_memory_gc();$$);





import { createClient, SupabaseClient } from '@supabase/supabase-js';   // :contentReference[oaicite:0]{index=0}
import { MemoryDriver, memoryDrivers }     from '../pangeaflow';

export interface SupabaseDriverOptions {
  /** Project URL, e.g. https://abc.supabase.co */
  url: string;
  /** anon or service key */
  anonKey: string;
  /** Postgres schema (defaults to 'public') */
  schema?: string;
  /** Table name (defaults to 'pangeaflow_memory') */
  table?: string;
  /** Logical namespace if you want to isolate multiple agents */
  namespace?: string;
}

class SupabaseMemoryDriver implements MemoryDriver {
  private readonly client: SupabaseClient;
  private readonly table: string;
  private readonly ns: string;

  constructor(opts: SupabaseDriverOptions) {
    if (!opts.url || !opts.anonKey) {
      throw new Error('SupabaseMemoryDriver requires url & anonKey');
    }
    this.client =  createClient(opts.url, opts.anonKey, { db: { schema:opts.schema ?? 'public' } }) as SupabaseClient;
    this.table = opts.table ?? 'pangeaflow_memory';
    this.ns    = opts.namespace ?? 'default';
  }

  private keyMatch(key: string) {
    return { namespace: this.ns, key };
  }

  async get<T>(key: string): Promise<T | undefined> {
    const { data, error } = await this.client
      .from(this.table)
      .select('value, expires')
      .match(this.keyMatch(key))
      .single();

    
    if (error?.code === 'PGRST116') return undefined;      // not found
    if (error) throw error;

    if (data.expires && Date.now() > data.expires) {       // expired
      await this.delete(key);
      return undefined;
    }
    return data.value as T;
  }

  async set<T>(key: string, value: T, ttlMs?: number): Promise<void> {
    const expires = ttlMs ? Date.now() + ttlMs : null;
    
    const {data, error } = await this.client
      .from(this.table)
      .upsert(
        { namespace: this.ns, key, value, expires },
        { onConflict: 'namespace,key' }                    // :contentReference[oaicite:1]{index=1}
      );

    if (error) throw error;
  }

  async delete(key: string): Promise<void> {
    const { error } = await this.client
      .from(this.table)
      .delete()
      .match(this.keyMatch(key));
      
    if (error) throw error;
  }

  async clear(namespace?: string): Promise<void> {
    const ns = namespace ?? this.ns;
    const { error } = await this.client
      .from(this.table)
      .delete()
      .eq('namespace', ns);
   
    if (error) throw error;
  }

  async keys(namespace?: string): Promise<string[]> {
    const ns = namespace ?? this.ns;
    const { data, error } = await this.client
      .from(this.table)
      .select('key')
      .eq('namespace', ns);


    if (error) throw error;
    return (data ?? []).map(r => r.key as string);
  }

  async close(): Promise<void> {
    /* supabase-js has no close(), so nothing to do */
  }
}

/** Call once at program start (or re-export from this module) */
export function registerSupabaseMemoryDriver(): void {
  memoryDrivers.register('supabase', (opts: SupabaseDriverOptions) => new SupabaseMemoryDriver(opts));
}
