/**
 * Example demonstrating the new factory methods for PangeaFlow agents.
 * 
 * This example shows how to use the new factory methods that provide a cleaner API
 * by putting business parameters first and hiding framework parameters.
 */

import {
  ReasoningAgent,
  ToolAgent,
  MemoryAgent,
  WorkflowBuilder,
  type AgentOptions,
  type MemoryAgentOptions,
  type RetryOpts
} from '../pangeaflow';

// ============================================================================
// EXAMPLE LLM PROVIDER
// ============================================================================

async function callLlm_openrouter(
  prompt: string,
  context: Record<string, unknown>
): Promise<string> {
  // Mock LLM implementation for example
  console.log('LLM called with prompt:', prompt.substring(0, 100) + '...');
  console.log('Context keys:', Object.keys(context));
  
  // Simulate LLM response
  return `Based on the context, I recommend the following actions: continue, analyze
  
  ACTIONS: continue, analyze`;
}

// ============================================================================
// FACTORY METHOD EXAMPLES
// ============================================================================

/**
 * Example 1: Creating agents using the new factory methods
 * Business parameters come first, framework parameters are hidden
 */
export function createAgentsWithFactoryMethods() {
  console.log('🏭 Creating agents using factory methods...');

  // ReasoningAgent - business params first (callLlm, retryOpts)
  const reasoningAgent = ReasoningAgent.create(
    'reasoning-agent',
    callLlm_openrouter,
    { maxAttempts: 3, baseDelayMs: 500 } // retry options
  );

  // ToolAgent - business params first (tools)
  const toolAgent = ToolAgent.create(
    'tool-agent',
    {
      'file_read': async (args: any) => `File content: ${args.path}`,
      'calculate': async (args: any) => args.a + args.b,
      'api_call': async (args: any) => ({ status: 'success', data: args.url })
    }
  );

  // MemoryAgent - business params first (memory options)
  const memoryAgent = MemoryAgent.create(
    'memory-agent',
    {
      driver: 'mem', // use in-memory driver
      defaultTTL: 3600000 // 1 hour TTL
    }
  );

  return { reasoningAgent, toolAgent, memoryAgent };
}

/**
 * Example 2: Advanced factory usage with custom options
 */
export function createAgentsWithAdvancedOptions() {
  console.log('⚙️ Creating agents with advanced options...');

  // Custom retry strategy for reasoning agent
  const retryOpts: RetryOpts = {
    maxAttempts: 5,
    baseDelayMs: 1000,
    factor: 1.5,
    jitter: true
  };

  const reasoningAgent = ReasoningAgent.create(
    'advanced-reasoning',
    callLlm_openrouter,
    retryOpts
  );

  // Tool agent with Map instead of Record
  const toolMap = new Map([
    ['complex_calculation', async (args: any) => {
      // Simulate complex calculation
      await new Promise(resolve => setTimeout(resolve, 100));
      return { result: args.x * args.y + args.z, timestamp: Date.now() };
    }],
    ['data_transform', async (args: any) => {
      return args.data.map((item: any) => ({ ...item, processed: true }));
    }]
  ]);

  const toolAgent = ToolAgent.create(
    'advanced-tools',
    toolMap
  );

  // Memory agent with custom driver options
  const memoryOpts: MemoryAgentOptions = {
    driver: 'mem', // Could be 'redis', 'postgres', etc. in production
    defaultTTL: 7200000 // 2 hours
  };

  const memoryAgent = MemoryAgent.create(
    'advanced-memory',
    memoryOpts
  );

  return { reasoningAgent, toolAgent, memoryAgent };
}

/**
 * Example 3: Building a complete workflow with factory-created agents
 */
export function buildWorkflowWithFactoryAgents() {
  console.log('🔧 Building workflow with factory-created agents...');

  // Create agents using factory methods
  const { reasoningAgent, toolAgent, memoryAgent } = createAgentsWithFactoryMethods();

  // Build workflow using the builder pattern
  // Note: The builder creates agents internally using its own methods
  const workflow = WorkflowBuilder
    .create()
    .addReasoningAgent('reasoning-agent' as any, callLlm_openrouter)
    .addToolAgent('tool-agent', {
      'file_read': async (args: any) => `File content: ${args.path}`,
      'calculate': async (args: any) => args.a + args.b,
      'api_call': async (args: any) => ({ status: 'success', data: args.url })
    })
    .addMemoryAgent('memory-agent')
    .route('start', 'reasoning-agent')
    .route('continue', 'tool-agent', 'memory-agent')
    .route('analyze', 'reasoning-agent', 'tool-agent')
    .build();

  return workflow;
}

/**
 * Example 4: Comparison between old constructor and new factory method
 */
export function comparisonExample() {
  console.log('📊 Comparing old vs new approach...');

  // ❌ OLD WAY: Constructor with framework params mixed with business params
  // Hard to remember parameter order, easy to mix up eventBus and telemetry
  /*
  const oldReasoningAgent = new ReasoningAgent(
    'reasoning' as ComponentId,
    eventBus,           // framework param
    telemetry,          // framework param  
    callLlm_openrouter, // business param
    { maxAttempts: 3 }  // business param
  );
  */

  // ✅ NEW WAY: Factory method with business params first
  // Clean API, framework params are hidden
  const newReasoningAgent = ReasoningAgent.create(
    'reasoning',
    callLlm_openrouter, // business param first
    { maxAttempts: 3 }  // business param second
  );

  console.log('✅ New factory method provides cleaner API');
  console.log('✅ Business parameters come first');
  console.log('✅ Framework parameters are hidden');
  console.log('✅ Less error-prone for developers');

  return newReasoningAgent;
}

/**
 * Example 5: Running a complete workflow
 */
export async function runWorkflowExample() {
  console.log('🚀 Running complete workflow example...');

  const workflow = buildWorkflowWithFactoryAgents();

  // Execute the workflow
  const results = await workflow.execute('start', {
    sharedState: {
      userInput: 'Analyze the sales data and generate a report',
      data: [
        { product: 'Widget A', sales: 100 },
        { product: 'Widget B', sales: 150 },
        { product: 'Widget C', sales: 75 }
      ]
    }
  });

  console.log('📈 Workflow completed with', results.length, 'steps');
  results.forEach((result, index) => {
    console.log(`Step ${index + 1}:`, {
      success: result.success,
      hasOutput: !!result.output,
      nextActions: result.nextActions
    });
  });

  return results;
}

// ============================================================================
// MAIN DEMO
// ============================================================================

if (require.main === module) {
  console.log('🎯 PangeaFlow Factory Methods Demo\n');
  
  // Run all examples
  createAgentsWithFactoryMethods();
  console.log('');
  
  createAgentsWithAdvancedOptions();
  console.log('');
  
  buildWorkflowWithFactoryAgents();
  console.log('');
  
  comparisonExample();
  console.log('');
  
  // Run the workflow
  runWorkflowExample().catch(console.error);
}
