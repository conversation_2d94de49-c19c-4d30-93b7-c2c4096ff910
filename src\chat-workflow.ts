/**
 * Chat Workflow - A specialized PangeaFlow workflow for conversational AI
 * 
 * This module creates a chat-optimized workflow using PangeaFlow's components
 * with conversation history management and the OpenRouter LLM integration.
 */

import { WorkflowB<PERSON>er, WorkflowOrchestrator, AgentComponent, ExecutionContext, ExecutionResult, EventBus, TelemetryCollector } from './pangeaflow/pangeaflow';
import { callLlm_openrouter } from './pangeaflow/utils/callLlm_openrouter';
import { SessionMemory } from './pangeaflow/utils/sessionMemory';

/**
 * Interface for chat message
 */
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
}

/**
 * Interface for chat session state
 */
export interface ChatSessionState {
  conversationId: string;
  messages: ChatMessage[];
  model: string;
  temperature: number;
}

/**
 * Custom Chat Agent that properly handles user input from shared state
 */
class ChatAgent extends AgentComponent {
  constructor(
    id: string,
    eventBus: EventBus,
    telemetry: TelemetryCollector,
    private sessionMemory: SessionMemory<ChatSessionState>,
    private conversationId: string
  ) {
    super(id, eventBus, telemetry);
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    return this.withTelemetry('chat_processing', async () => {
      // Get the user input from shared state
      const userInput = context.sharedState.userInput as string;

      if (!userInput) {
        throw new Error('No user input provided in shared state');
      }

      const sessionState = this.sessionMemory.get(this.conversationId);

      if (!sessionState) {
        throw new Error('Session not found');
      }

      // Build conversation history for the LLM
      const conversationPrompt = this.buildConversationPrompt(sessionState.messages, userInput);

      try {
        const response = await callLlm_openrouter({
          prompt: conversationPrompt,
          temperature: sessionState.temperature,
          model: sessionState.model,
          tutorial_id: this.conversationId,
          use_cache: false // Disable cache for real-time chat
        });

        // Update session with new messages
        const updatedMessages = [
          ...sessionState.messages,
          {
            role: 'user' as const,
            content: userInput,
            timestamp: Date.now()
          },
          {
            role: 'assistant' as const,
            content: response,
            timestamp: Date.now()
          }
        ];

        this.sessionMemory.update(this.conversationId, {
          ...sessionState,
          messages: updatedMessages
        });

        this.emit('chat.completed', { userInput, response }, context.id);

        return {
          success: true,
          output: response,
          events: [],
          nextActions: ['continue'],
          sharedStateUpdates: {
            lastResponse: response,
            messageCount: updatedMessages.length
          },
        };
      } catch (error) {
        console.error('Error calling LLM:', error);
        const errorMessage = 'I apologize, but I encountered an error processing your request. Please try again.';

        return {
          success: false,
          output: errorMessage,
          error: error as Error,
          events: [],
          nextActions: ['error'],
          sharedStateUpdates: {
            lastError: (error as Error).message
          },
        };
      }
    });
  }

  /**
   * Builds a conversation prompt including message history
   */
  private buildConversationPrompt(messages: ChatMessage[], newMessage: string): string {
    let prompt = "You are a helpful AI assistant. Please provide thoughtful and accurate responses.\n\n";

    // Add conversation history (limit to last 10 messages to avoid token limits)
    const recentMessages = messages.slice(-10);

    if (recentMessages.length > 0) {
      prompt += "Conversation history:\n";
      for (const message of recentMessages) {
        const roleLabel = message.role === 'user' ? 'Human' : 'Assistant';
        prompt += `${roleLabel}: ${message.content}\n`;
      }
      prompt += "\n";
    }

    prompt += `Human: ${newMessage}\nAssistant:`;

    return prompt;
  }
}

/**
 * Chat workflow class that manages conversational AI using PangeaFlow
 */
export class ChatWorkflow {
  private workflow: WorkflowOrchestrator;
  private sessionMemory: SessionMemory<ChatSessionState>;
  private currentConversationId: string;

  constructor(
    conversationId: string = `chat-${Date.now()}`,
    model: string = "google/gemini-2.5-flash-preview",
    temperature: number = 0.7
  ) {
    this.currentConversationId = conversationId;
    this.sessionMemory = new SessionMemory<ChatSessionState>();
    
    // Initialize session if it doesn't exist
    if (!this.sessionMemory.hasConversation(conversationId)) {
      this.sessionMemory.update(conversationId, {
        conversationId,
        messages: [],
        model,
        temperature
      });
    }

    this.workflow = this.createChatWorkflow();
  }

  /**
   * Creates the PangeaFlow workflow for chat
   */
  private createChatWorkflow(): WorkflowOrchestrator {
    const orchestrator = new WorkflowOrchestrator();

    // Create our custom chat agent
    const chatAgent = new ChatAgent(
      'chat-agent',
      orchestrator['eventBus'],
      orchestrator['telemetry'],
      this.sessionMemory,
      this.currentConversationId
    );

    // Register the chat agent
    orchestrator.registerComponent(chatAgent);

    // Define routes
    orchestrator.defineRoute('chat', ['chat-agent' as any]);
    orchestrator.defineRoute('continue', ['chat-agent' as any]);

    return orchestrator;
  }



  /**
   * Sends a message and gets a response
   */
  async sendMessage(message: string): Promise<string> {
    try {
      const results = await this.workflow.execute('chat', {
        sharedState: {
          userInput: message,
          conversationId: this.currentConversationId
        }
      });

      // Extract the response from the workflow results
      const chatResult = results.find(result => result.output && typeof result.output === 'string');
      return (chatResult?.output as string) || 'I apologize, but I could not generate a response.';
    } catch (error) {
      console.error('Error in chat workflow:', error);
      return 'I encountered an error processing your message. Please try again.';
    }
  }

  /**
   * Gets the current conversation history
   */
  getConversationHistory(): ChatMessage[] {
    const sessionState = this.sessionMemory.get(this.currentConversationId);
    return sessionState?.messages || [];
  }

  /**
   * Clears the current conversation
   */
  clearConversation(): void {
    const sessionState = this.sessionMemory.get(this.currentConversationId);
    if (sessionState) {
      this.sessionMemory.update(this.currentConversationId, {
        ...sessionState,
        messages: []
      });
    }
  }

  /**
   * Gets workflow metrics for monitoring
   */
  getMetrics() {
    return this.workflow.getMetrics();
  }

  /**
   * Sets up event listeners for monitoring
   */
  setupEventListeners() {
    this.workflow.on('chat.completed', (event) => {
      // Optional: Add logging or monitoring here
      // console.log('💬 Chat completed');
    });

    this.workflow.on('step.completed', (event) => {
      // Optional: Add logging or monitoring here
      // console.log('✅ Step completed');
    });
  }
}
